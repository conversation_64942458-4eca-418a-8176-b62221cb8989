import React, { useContext, useRef, useLayoutEffect, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { NotesContext } from '../context/NotesContext';
import BreadcrumbNav from '../components/BreadcrumbNav';
import RichTextEditor, { RichTextEditorRef } from '../components/RichTextEditor';
import TagManager from '../components/TagManager';
import { getNoteAncestors, getNextSibling, getPreviousSibling } from '../utils/treeHelpers';
import { v4 as uuidv4 } from 'uuid';
import type { Note } from '../types';

const WorkflowyList: React.FC<{
  noteId: string;
  notes: Record<string, Note>;
  focusedId: string | null;
  setFocusedId: (id: string | null) => void;
  dispatch: any;
  parentId: string | null;
  pendingFocusId?: string | null;
  onFocusHandled?: () => void;
}> = ({ noteId, notes, focusedId, setFocusedId, dispatch, parentId, pendingFocusId, onFocusHandled }) => {
  const note = notes[noteId];
  const editorRef = useRef<RichTextEditorRef>(null);

  // Focus handling with improved reliability
  useLayoutEffect(() => {
    if (note && focusedId === noteId && editorRef.current) {
      editorRef.current.focus();
      editorRef.current.setCursorToEnd();
      if (pendingFocusId === noteId && onFocusHandled) {
        onFocusHandled();
      }
    }
  }, [focusedId, noteId, note, pendingFocusId, onFocusHandled]);

  if (!note) return null;

  // Handlers
  const handleChange = (content: string) => {
    dispatch({ type: 'update', id: noteId, updates: { content } });
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    console.log('NoteDetailPage handleKeyDown:', e.key); // Debug log
    if (e.key === 'Enter') {
      e.preventDefault();
      // Create new sibling note below
      const newId = uuidv4();
      const now = new Date().toISOString();
      console.log('Creating new note with ID:', newId); // Debug log
      dispatch({
        type: 'create',
        note: {
          id: newId,
          content: '',
          parentId: note.parentId,
          children: [],
          tags: [],
          createdAt: now,
          updatedAt: now,
        },
      });
      console.log('Setting focused ID to:', newId); // Debug log
      setFocusedId(newId);
      // Pass newId as pendingFocusId to children
      if (onFocusHandled) onFocusHandled();
    } else if (e.key === 'Tab' && !e.shiftKey) {
      e.preventDefault();
      // Indent: make this note a child of previous sibling
      const previousSibling = getPreviousSibling(noteId, notes);
      if (previousSibling) {
        dispatch({ type: 'moveSubtree', id: noteId, newParentId: previousSibling });
      }
    } else if (e.key === 'Tab' && e.shiftKey) {
      e.preventDefault();
      // Outdent: move to parent's parent
      if (note.parentId) {
        const parent = notes[note.parentId];
        dispatch({ type: 'moveSubtree', id: noteId, newParentId: parent.parentId || null });
      }
    } else if (e.key === 'Backspace' && note.content === '') {
      e.preventDefault();
      // Delete this note if empty
      const previousSibling = getPreviousSibling(noteId, notes);
      const nextSibling = getNextSibling(noteId, notes);
      dispatch({ type: 'delete', id: noteId });
      // Focus on previous sibling, or next sibling, or parent
      setFocusedId(previousSibling || nextSibling || parentId);
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      const nextSibling = getNextSibling(noteId, notes);
      if (nextSibling) {
        setFocusedId(nextSibling);
      }
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      const previousSibling = getPreviousSibling(noteId, notes);
      if (previousSibling) {
        setFocusedId(previousSibling);
      }
    }
  };

  return (
    <li className="ml-4 list-none relative">
      <div className="flex items-start">
        {/* Custom bullet point */}
        <div className="flex-shrink-0 w-2 h-2 bg-gray-400 rounded-full mt-2 mr-3"></div>
        <RichTextEditor
          ref={editorRef}
          value={note.content}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          onFocus={() => setFocusedId(noteId)}
          placeholder="(empty)"
          autoFocus={focusedId === noteId}
          className="flex-1"
        />
      </div>
      {note.children.length > 0 && (
        <ul className="ml-5">
          {note.children.map(childId => (
            <WorkflowyList
              key={childId}
              noteId={childId}
              notes={notes}
              focusedId={focusedId}
              setFocusedId={setFocusedId}
              dispatch={dispatch}
              parentId={noteId}
              pendingFocusId={pendingFocusId}
              onFocusHandled={onFocusHandled}
            />
          ))}
        </ul>
      )}
    </li>
  );
};

const NoteDetailContent: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { state, dispatch } = useContext(NotesContext)!;
  const navigate = useNavigate();
  const [focusedId, setFocusedId] = React.useState<string | null>(id || null);
  const [pendingFocusId, setPendingFocusId] = React.useState<string | null>(null);

  const handleFocusHandled = () => setPendingFocusId(null);

  // Get all available tags from all notes
  const availableTags = useMemo(() => {
    const allTags = new Set<string>();
    Object.values(state.notes).forEach(note => {
      note.tags.forEach(tag => allTags.add(tag));
    });
    return Array.from(allTags).sort();
  }, [state.notes]);

  // Build ancestors array for breadcrumbs using utility function
  const ancestors = useMemo(() => {
    if (!id) return [];
    return getNoteAncestors(id, state.notes);
  }, [id, state.notes]);

  if (!id || !state.notes[id]) {
    return <div className="p-8 text-gray-300">Note not found.</div>;
  }

  const note = state.notes[id];

  const handleCrumbClick = (noteId: string) => {
    navigate(`/notes/${noteId}`);
  };

  const handleTagsChange = (tags: string[]) => {
    dispatch({ type: 'update', id: note.id, updates: { tags } });
  };

  return (
    <div className="p-8 max-w-3xl mx-auto min-h-screen">
      <div className="mb-4">
        <button onClick={() => navigate('/notes')} className="text-gray-400 hover:text-blue-400 mr-2">&larr; Back to Notes</button>
        <BreadcrumbNav ancestors={ancestors} onCrumbClick={handleCrumbClick} />
      </div>
      <div className="bg-[#23272f] rounded-xl border border-[#23272f] shadow-sm p-6">
        {/* Tags Section */}
        <div className="mb-6">
          <TagManager
            tags={note.tags}
            onTagsChange={handleTagsChange}
            availableTags={availableTags}
          />
        </div>

        <ul className="pl-4">
          <WorkflowyList
            noteId={note.id}
            notes={state.notes}
            focusedId={focusedId}
            setFocusedId={id => {
              setFocusedId(id);
              setPendingFocusId(id);
            }}
            dispatch={dispatch}
            parentId={note.parentId}
            pendingFocusId={pendingFocusId}
            onFocusHandled={handleFocusHandled}
          />
        </ul>
      </div>
    </div>
  );
};

const NoteDetailPage: React.FC = () => <NoteDetailContent />;
export default NoteDetailPage; 